package com.ray.common;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-27
 */
public class ArrayList<T> {
    private T[] values ;

    private int size = 0;
    private static final int INIT_CAPACITY = 1;

    public ArrayList() {
        this(INIT_CAPACITY);
    }

    public ArrayList(int size) {
        if(size <= 0){
            throw new IllegalArgumentException("size must > 0");
        }
        this.size = 0;
        this.values = (T[]) new Object[size];
    }

    public T get(int index) {
        return values[index];
    }

    public void addAtHead(int val) {

    }

    public void addAtTail(int val) {

    }

    public void addAtIndex(int index, int val) {

    }

    public void deleteAtIndex(int index) {

    }
}
