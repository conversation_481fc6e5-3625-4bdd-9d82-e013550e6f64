package com.ray.common;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-25
 */
public class TreeNode {
    public int val;
    public TreeNode left;
    public TreeNode right;

    public TreeNode() {
    }

    public TreeNode(int x) {
        val = x;
    }

    public TreeNode(int x, TreeNode left, TreeNode right) {
        val = x;
        this.left = left;
        this.right = right;
    }


    // 按照力扣的序列化规则输入生成二叉树，返回根节点
    public static TreeNode createRoot(Integer[] values) {
        if (values.length == 0) {
            return null;
        }
        TreeNode root = new TreeNode(values[0]);
        TreeNode[] tree = new TreeNode[values.length];
        tree[0] = root;
        for (int i = 1; i < values.length; i++) {
            if (values[i] != null) {
                tree[i] = new TreeNode(values[i]);
            }
        }
        for (int i = 0; i < values.length; i++) {
            if (tree[i] != null) {
                if (2 * i + 1 < values.length) {
                    tree[i].left = tree[2 * i + 1];
                }
                if (2 * i + 2 < values.length) {
                    tree[i].right = tree[2 * i + 2];
                }
            }
        }
        return root;
    }
}
