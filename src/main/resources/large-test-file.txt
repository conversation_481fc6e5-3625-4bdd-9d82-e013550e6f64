大文件测试数据 - 用于性能对比测试

=== 数据集1：用户信息 ===
ID,姓名,年龄,性别,城市,职业,薪资,入职日期
1001,张三,25,男,北京,软件工程师,15000,2020-01-15
1002,李四,30,女,上海,产品经理,18000,2019-03-20
1003,王五,28,男,广州,UI设计师,12000,2020-06-10
1004,赵六,35,女,深圳,项目经理,22000,2018-09-05
1005,钱七,22,男,杭州,前端开发,13000,2021-02-28
1006,孙八,29,女,成都,后端开发,16000,2019-11-12
1007,周九,31,男,武汉,测试工程师,14000,2020-04-18
1008,吴十,27,女,西安,运维工程师,17000,2019-07-22
1009,郑一,26,男,南京,数据分析师,19000,2020-10-30
1010,王二,33,女,重庆,架构师,25000,2018-05-14

=== 数据集2：销售记录 ===
订单号,商品名称,数量,单价,总价,客户ID,销售日期,销售员
ORD001,笔记本电脑,2,5999.00,11998.00,C001,2023-01-15,张销售
ORD002,无线鼠标,5,199.00,995.00,C002,2023-01-16,李销售
ORD003,机械键盘,3,599.00,1797.00,C003,2023-01-17,王销售
ORD004,显示器,1,2999.00,2999.00,C004,2023-01-18,赵销售
ORD005,耳机,4,299.00,1196.00,C005,2023-01-19,钱销售
ORD006,手机,1,3999.00,3999.00,C006,2023-01-20,孙销售
ORD007,平板电脑,2,2599.00,5198.00,C007,2023-01-21,周销售
ORD008,智能手表,3,1999.00,5997.00,C008,2023-01-22,吴销售
ORD009,蓝牙音箱,6,399.00,2394.00,C009,2023-01-23,郑销售
ORD010,移动电源,8,199.00,1592.00,C010,2023-01-24,王销售

=== 数据集3：日志记录 ===
2023-01-15 08:30:15 [INFO] 系统启动成功
2023-01-15 08:30:16 [INFO] 数据库连接建立
2023-01-15 08:30:17 [INFO] 缓存服务初始化完成
2023-01-15 08:30:18 [INFO] Web服务器启动，监听端口8080
2023-01-15 08:35:22 [INFO] 用户登录：user001
2023-01-15 08:35:45 [INFO] 用户查询商品列表
2023-01-15 08:36:12 [WARN] 商品库存不足：商品ID=P001
2023-01-15 08:36:30 [INFO] 订单创建成功：订单号=ORD001
2023-01-15 08:37:15 [INFO] 支付处理中：订单号=ORD001
2023-01-15 08:37:45 [INFO] 支付成功：订单号=ORD001，金额=11998.00
2023-01-15 08:38:00 [INFO] 发送确认邮件：<EMAIL>
2023-01-15 08:40:22 [ERROR] 邮件发送失败：SMTP连接超时
2023-01-15 08:40:30 [INFO] 重试发送邮件
2023-01-15 08:40:35 [INFO] 邮件发送成功
2023-01-15 08:45:10 [INFO] 用户登出：user001

=== 数据集4：配置信息 ===
# 数据库配置
database.host=localhost
database.port=3306
database.name=testdb
database.username=root
database.password=123456
database.pool.min=5
database.pool.max=20
database.timeout=30000

# Redis配置
redis.host=127.0.0.1
redis.port=6379
redis.password=
redis.database=0
redis.timeout=5000
redis.pool.max=50

# 应用配置
app.name=TestApplication
app.version=1.0.0
app.debug=true
app.log.level=INFO
app.upload.path=/tmp/uploads
app.upload.max.size=10485760

=== 数据集5：JSON格式数据 ===
{"id":1,"name":"张三","age":25,"email":"<EMAIL>","skills":["Java","Spring","MySQL"]}
{"id":2,"name":"李四","age":30,"email":"<EMAIL>","skills":["Python","Django","PostgreSQL"]}
{"id":3,"name":"王五","age":28,"email":"<EMAIL>","skills":["JavaScript","React","MongoDB"]}
{"id":4,"name":"赵六","age":35,"email":"<EMAIL>","skills":["Go","Gin","Redis"]}
{"id":5,"name":"钱七","age":22,"email":"<EMAIL>","skills":["C++","Qt","SQLite"]}

=== 数据集6：XML格式数据 ===
<users>
  <user id="1">
    <name>张三</name>
    <age>25</age>
    <city>北京</city>
    <department>技术部</department>
  </user>
  <user id="2">
    <name>李四</name>
    <age>30</age>
    <city>上海</city>
    <department>产品部</department>
  </user>
  <user id="3">
    <name>王五</name>
    <age>28</age>
    <city>广州</city>
    <department>设计部</department>
  </user>
</users>

=== 数据集7：重复数据块（用于测试大文件处理） ===
BLOCK_START_001
这是一个重复的数据块，用于测试大文件的读写性能。
包含中文字符、英文字符、数字和特殊符号。
Data Block Content: ABCDEFGHIJKLMNOPQRSTUVWXYZ
Numbers: 0123456789
Special Characters: !@#$%^&*()_+-=[]{}|;:,.<>?
BLOCK_END_001

BLOCK_START_002
这是另一个重复的数据块，用于增加文件大小。
测试BIO和NIO在处理大文件时的性能差异。
Lorem ipsum dolor sit amet, consectetur adipiscing elit.
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
BLOCK_END_002

=== 文件结束 ===
总行数：约100行
总字符数：约5000字符
用途：BIO和NIO文件操作性能测试
