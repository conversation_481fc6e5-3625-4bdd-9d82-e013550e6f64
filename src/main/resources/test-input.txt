Java I/O 测试文件

这是一个用于测试Java BIO和NIO文件操作的示例文件。

=== BIO (Blocking I/O) 特点 ===
1. 同步阻塞I/O模型
2. 面向流的处理方式
3. 一个线程处理一个I/O操作
4. 编程模型简单直观
5. 适合连接数较少的场景

=== NIO (Non-blocking I/O) 特点 ===
1. 同步非阻塞I/O模型
2. 面向缓冲区的处理方式
3. 一个线程可以处理多个I/O操作
4. 基于选择器的多路复用
5. 适合高并发场景

=== 文件I/O性能对比 ===
- BIO: 简单易用，但在大文件或频繁I/O时性能较低
- NIO: 复杂但高效，特别适合大文件处理和批量操作

=== 示例数据 ===
用户ID,姓名,年龄,城市
1001,张三,25,北京
1002,李四,30,上海
1003,王五,28,广州
1004,赵六,35,深圳
1005,钱七,22,杭州
1006,孙八,29,成都
1007,周九,31,武汉
1008,吴十,27,西安

=== 多行文本测试 ===
这是第一行文本内容
这是第二行文本内容
这是第三行文本内容
这是第四行文本内容
这是第五行文本内容

=== 特殊字符测试 ===
中文字符：你好世界
英文字符：Hello World
数字字符：1234567890
特殊符号：!@#$%^&*()_+-=[]{}|;:,.<>?
Unicode字符：🌟⭐✨💫🔥💯

=== 长文本段落测试 ===
Java NIO (New I/O) 是从Java 1.4版本开始引入的一套新的I/O API，可以替代标准的Java I/O API。
NIO与原来的I/O有同样的作用和目的，但是使用的方式完全不同，NIO支持面向缓冲区的、基于通道的I/O操作。
NIO将以更加高效的方式进行文件的读写操作。Java NIO系统的核心在于：通道(Channel)和缓冲区(Buffer)。
通道表示打开到 I/O 设备(例如：文件、套接字)的连接。若需要使用 NIO 系统，需要获取用于连接 I/O 设备的通道以及用于容纳数据的缓冲区。
然后操作缓冲区，对数据进行处理。简而言之，Channel 负责传输， Buffer 负责存储。

=== 文件结束标记 ===
EOF - End of File
