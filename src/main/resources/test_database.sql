-- 测试数据库表创建和数据插入脚本
-- 创建数据库（如果需要）
-- CREATE DATABASE IF NOT EXISTS test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE test_db;

-- 删除已存在的表
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS users;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID，主键',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    age INT COMMENT '年龄',
    gender ENUM('M', 'F', 'OTHER') DEFAULT 'OTHER' COMMENT '性别',
    salary DECIMAL(10,2) COMMENT '薪资',
    department_id INT COMMENT '部门ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-活跃，0-非活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    profile_data JSON COMMENT '用户档案数据'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID，主键',
    order_no VARCHAR(32) NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_category VARCHAR(50) COMMENT '商品分类',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '折扣金额',
    payment_method ENUM('CASH', 'CARD', 'ALIPAY', 'WECHAT') DEFAULT 'ALIPAY' COMMENT '支付方式',
    order_status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '订单状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    shipped_at DATETIME COMMENT '发货时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 创建用户表索引
-- 普通索引
CREATE INDEX idx_users_department ON users(department_id);
CREATE INDEX idx_users_status ON users(status);

-- 唯一索引
CREATE UNIQUE INDEX uk_users_email ON users(email);

-- 联合索引
CREATE INDEX idx_users_age_salary ON users(age, salary);
CREATE INDEX idx_users_created_status ON users(created_at, status);

-- 创建订单表索引
-- 普通索引
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_category ON orders(product_category);

-- 唯一索引
CREATE UNIQUE INDEX uk_orders_order_no ON orders(order_no);

-- 联合索引
CREATE INDEX idx_orders_status_created ON orders(order_status, created_at);
CREATE INDEX idx_orders_user_amount ON orders(user_id, total_amount);

-- 添加外键约束
ALTER TABLE orders ADD CONSTRAINT fk_orders_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 插入用户测试数据（40000条）
DELIMITER $$
CREATE PROCEDURE InsertUsers()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE rand_username VARCHAR(50);
    DECLARE rand_email VARCHAR(100);
    DECLARE rand_phone VARCHAR(20);
    DECLARE rand_age INT;
    DECLARE rand_gender ENUM('M', 'F', 'OTHER');
    DECLARE rand_salary DECIMAL(10,2);
    DECLARE rand_dept INT;
    DECLARE rand_status TINYINT;
    DECLARE rand_login DATETIME;
    
    WHILE i <= 40000 DO
        SET rand_username = CONCAT('user_', LPAD(i, 6, '0'));
        SET rand_email = CONCAT('user', i, '@example', FLOOR(RAND() * 10), '.com');
        SET rand_phone = CONCAT('1', FLOOR(RAND() * 9) + 1, LPAD(FLOOR(RAND() * 100000000), 8, '0'));
        SET rand_age = FLOOR(RAND() * 50) + 18;
        SET rand_gender = ELT(FLOOR(RAND() * 3) + 1, 'M', 'F', 'OTHER');
        SET rand_salary = ROUND(RAND() * 50000 + 3000, 2);
        SET rand_dept = FLOOR(RAND() * 20) + 1;
        SET rand_status = IF(RAND() > 0.1, 1, 0);
        SET rand_login = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 365) DAY);
        
        INSERT INTO users (username, email, phone, age, gender, salary, department_id, status, last_login_time, profile_data)
        VALUES (
            rand_username,
            rand_email,
            rand_phone,
            rand_age,
            rand_gender,
            rand_salary,
            rand_dept,
            rand_status,
            rand_login,
            JSON_OBJECT('level', FLOOR(RAND() * 5) + 1, 'points', FLOOR(RAND() * 10000))
        );
        
        SET i = i + 1;
        
        -- 每1000条提交一次，避免事务过大
        IF i % 1000 = 0 THEN
            COMMIT;
        END IF;
    END WHILE;
END$$
DELIMITER ;

-- 调用存储过程插入用户数据
CALL InsertUsers();
DROP PROCEDURE InsertUsers;

-- 插入订单测试数据（50000条）
DELIMITER $$
CREATE PROCEDURE InsertOrders()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE rand_order_no VARCHAR(32);
    DECLARE rand_user_id BIGINT;
    DECLARE rand_product VARCHAR(200);
    DECLARE rand_category VARCHAR(50);
    DECLARE rand_quantity INT;
    DECLARE rand_unit_price DECIMAL(10,2);
    DECLARE rand_total DECIMAL(12,2);
    DECLARE rand_discount DECIMAL(10,2);
    DECLARE rand_payment ENUM('CASH', 'CARD', 'ALIPAY', 'WECHAT');
    DECLARE rand_status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'CANCELLED');
    DECLARE rand_shipped DATETIME;
    DECLARE max_user_id BIGINT;

    -- 获取最大用户ID
    SELECT MAX(id) INTO max_user_id FROM users;

    WHILE i <= 50000 DO
        SET rand_order_no = CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(i, 8, '0'));
        SET rand_user_id = FLOOR(RAND() * max_user_id) + 1;
        SET rand_product = CONCAT('Product_', ELT(FLOOR(RAND() * 10) + 1,
            'Laptop', 'Phone', 'Tablet', 'Watch', 'Headphone',
            'Camera', 'Speaker', 'Monitor', 'Keyboard', 'Mouse'));
        SET rand_category = ELT(FLOOR(RAND() * 5) + 1,
            'Electronics', 'Clothing', 'Books', 'Home', 'Sports');
        SET rand_quantity = FLOOR(RAND() * 5) + 1;
        SET rand_unit_price = ROUND(RAND() * 2000 + 10, 2);
        SET rand_discount = ROUND(RAND() * 100, 2);
        SET rand_total = ROUND(rand_quantity * rand_unit_price - rand_discount, 2);
        SET rand_payment = ELT(FLOOR(RAND() * 4) + 1, 'CASH', 'CARD', 'ALIPAY', 'WECHAT');
        SET rand_status = ELT(FLOOR(RAND() * 5) + 1, 'PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'CANCELLED');
        SET rand_shipped = IF(rand_status IN ('SHIPPED', 'DELIVERED'),
            DATE_ADD(NOW() - INTERVAL FLOOR(RAND() * 30) DAY, INTERVAL FLOOR(RAND() * 24) HOUR),
            NULL);

        INSERT INTO orders (order_no, user_id, product_name, product_category, quantity,
                          unit_price, total_amount, discount_amount, payment_method,
                          order_status, shipped_at)
        VALUES (
            rand_order_no,
            rand_user_id,
            rand_product,
            rand_category,
            rand_quantity,
            rand_unit_price,
            rand_total,
            rand_discount,
            rand_payment,
            rand_status,
            rand_shipped
        );

        SET i = i + 1;

        -- 每1000条提交一次，避免事务过大
        IF i % 1000 = 0 THEN
            COMMIT;
        END IF;
    END WHILE;
END$$
DELIMITER ;

-- 调用存储过程插入订单数据
CALL InsertOrders();
DROP PROCEDURE InsertOrders;

-- 创建一些统计查询示例
-- 查看表数据统计
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'orders' as table_name, COUNT(*) as record_count FROM orders;

-- 查看索引使用情况示例查询
-- 1. 通过部门查询用户
-- EXPLAIN SELECT * FROM users WHERE department_id = 5;

-- 2. 通过邮箱查询用户（唯一索引）
-- EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';

-- 3. 通过年龄和薪资范围查询（联合索引）
-- EXPLAIN SELECT * FROM users WHERE age BETWEEN 25 AND 35 AND salary > 20000;

-- 4. 查询用户的订单（关联查询）
-- EXPLAIN SELECT u.username, o.order_no, o.total_amount
-- FROM users u JOIN orders o ON u.id = o.user_id
-- WHERE u.department_id = 10;

-- 5. 按订单状态和时间查询（联合索引）
-- EXPLAIN SELECT * FROM orders WHERE order_status = 'DELIVERED' AND created_at >= '2024-01-01';

COMMIT;
